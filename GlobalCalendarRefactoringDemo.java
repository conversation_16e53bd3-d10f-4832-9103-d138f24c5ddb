import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * GlobalCalendar重构演示
 * 展示从多记录设计到单记录设计的转换
 */
public class GlobalCalendarRefactoringDemo {
    
    public static void main(String[] args) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 演示新的单记录设计
        demonstrateNewDesign(objectMapper);
    }
    
    /**
     * 演示新的单记录设计
     */
    private static void demonstrateNewDesign(ObjectMapper objectMapper) throws Exception {
        System.out.println("=== GlobalCalendar 单记录设计演示 ===");
        
        // 1. 创建GlobalCalendar对象
        GlobalCalendar globalCalendar = new GlobalCalendar();
        
        // 设置假期列表
        List<LocalDate> holidays = Arrays.asList(
            LocalDate.of(2024, 1, 1),   // 元旦
            LocalDate.of(2024, 2, 10),  // 春节
            LocalDate.of(2024, 4, 5),   // 清明节
            LocalDate.of(2024, 5, 1),   // 劳动节
            LocalDate.of(2024, 10, 1),  // 国庆节
            LocalDate.of(2024, 12, 25)  // 圣诞节
        );
        globalCalendar.setHolidays(holidays);
        
        // 设置夏令时间
        TimeRange summerTime = new TimeRange(
            LocalDate.of(2024, 6, 1), 
            LocalDate.of(2024, 8, 31), 
            "夏令时间", 
            true
        );
        globalCalendar.setSummerTime(summerTime);
        
        // 设置冬令时间
        TimeRange winterTime = new TimeRange(
            LocalDate.of(2024, 12, 1), 
            LocalDate.of(2025, 2, 28), 
            "冬令时间", 
            true
        );
        globalCalendar.setWinterTime(winterTime);
        
        // 2. 演示JSON序列化（模拟存储到数据库）
        System.out.println("\n--- JSON序列化演示 ---");
        
        String holidaysJson = objectMapper.writeValueAsString(globalCalendar.getHolidays());
        System.out.println("假期列表JSON: " + holidaysJson);
        
        String summerTimeJson = objectMapper.writeValueAsString(globalCalendar.getSummerTime());
        System.out.println("夏令时间JSON: " + summerTimeJson);
        
        String winterTimeJson = objectMapper.writeValueAsString(globalCalendar.getWinterTime());
        System.out.println("冬令时间JSON: " + winterTimeJson);
        
        // 3. 演示JSON反序列化（模拟从数据库读取）
        System.out.println("\n--- JSON反序列化演示 ---");
        
        // 模拟从数据库读取的JSON数据
        GlobalCalendarEntity entity = new GlobalCalendarEntity("default");
        entity.setHolidays(holidaysJson);
        entity.setSummerTime(summerTimeJson);
        entity.setWinterTime(winterTimeJson);
        
        // 转换回GlobalCalendar对象
        GlobalCalendar restoredCalendar = convertEntityToGlobalCalendar(entity, objectMapper);
        
        // 4. 验证数据完整性
        System.out.println("\n--- 数据完整性验证 ---");
        System.out.println("原始假期数量: " + globalCalendar.getHolidays().size());
        System.out.println("恢复假期数量: " + restoredCalendar.getHolidays().size());
        System.out.println("假期数据一致: " + globalCalendar.getHolidays().equals(restoredCalendar.getHolidays()));
        
        System.out.println("夏令时间一致: " + compareTimes(globalCalendar.getSummerTime(), restoredCalendar.getSummerTime()));
        System.out.println("冬令时间一致: " + compareTimes(globalCalendar.getWinterTime(), restoredCalendar.getWinterTime()));
        
        // 5. 演示业务功能
        System.out.println("\n--- 业务功能演示 ---");
        LocalDate testDate = LocalDate.of(2024, 7, 15);
        System.out.println("测试日期: " + testDate);
        System.out.println("是否为假期: " + restoredCalendar.isHoliday(testDate));
        System.out.println("是否为夏令时间: " + restoredCalendar.isSummerTime(testDate));
        System.out.println("是否为冬令时间: " + restoredCalendar.isWinterTime(testDate));
        
        System.out.println("\n=== 重构演示完成 ===");
    }
    
    /**
     * 模拟Service中的转换方法
     */
    private static GlobalCalendar convertEntityToGlobalCalendar(GlobalCalendarEntity entity, ObjectMapper objectMapper) throws Exception {
        GlobalCalendar globalCalendar = new GlobalCalendar();
        
        // 解析假期列表
        if (entity.getHolidays() != null && !entity.getHolidays().trim().isEmpty()) {
            List<LocalDate> holidays = objectMapper.readValue(entity.getHolidays(), 
                objectMapper.getTypeFactory().constructCollectionType(List.class, LocalDate.class));
            globalCalendar.setHolidays(holidays);
        }
        
        // 解析夏令时间
        if (entity.getSummerTime() != null && !entity.getSummerTime().trim().isEmpty()) {
            TimeRange summerTime = objectMapper.readValue(entity.getSummerTime(), TimeRange.class);
            globalCalendar.setSummerTime(summerTime);
        }
        
        // 解析冬令时间
        if (entity.getWinterTime() != null && !entity.getWinterTime().trim().isEmpty()) {
            TimeRange winterTime = objectMapper.readValue(entity.getWinterTime(), TimeRange.class);
            globalCalendar.setWinterTime(winterTime);
        }
        
        return globalCalendar;
    }
    
    /**
     * 比较两个TimeRange对象
     */
    private static boolean compareTimes(TimeRange time1, TimeRange time2) {
        if (time1 == null && time2 == null) return true;
        if (time1 == null || time2 == null) return false;
        
        return time1.getStartDate().equals(time2.getStartDate()) && 
               time1.getEndDate().equals(time2.getEndDate());
    }
}

/**
 * 模拟GlobalCalendar类的简化版本
 */
class GlobalCalendar {
    private List<LocalDate> holidays;
    private TimeRange summerTime;
    private TimeRange winterTime;
    
    public List<LocalDate> getHolidays() { return holidays; }
    public void setHolidays(List<LocalDate> holidays) { this.holidays = holidays; }
    
    public TimeRange getSummerTime() { return summerTime; }
    public void setSummerTime(TimeRange summerTime) { this.summerTime = summerTime; }
    
    public TimeRange getWinterTime() { return winterTime; }
    public void setWinterTime(TimeRange winterTime) { this.winterTime = winterTime; }
    
    public boolean isHoliday(LocalDate date) {
        return holidays != null && holidays.contains(date);
    }
    
    public boolean isSummerTime(LocalDate date) {
        return summerTime != null && summerTime.contains(date);
    }
    
    public boolean isWinterTime(LocalDate date) {
        return winterTime != null && winterTime.contains(date);
    }
}

/**
 * 模拟TimeRange类的简化版本
 */
class TimeRange {
    private LocalDate startDate;
    private LocalDate endDate;
    private String description;
    private boolean enabled;
    
    public TimeRange() {}
    
    public TimeRange(LocalDate startDate, LocalDate endDate, String description, boolean enabled) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.description = description;
        this.enabled = enabled;
    }
    
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public boolean contains(LocalDate date) {
        return date != null && startDate != null && endDate != null &&
               !date.isBefore(startDate) && !date.isAfter(endDate);
    }
}

/**
 * 模拟GlobalCalendarEntity类的简化版本
 */
class GlobalCalendarEntity {
    private String bizId;
    private String holidays;
    private String summerTime;
    private String winterTime;
    
    public GlobalCalendarEntity(String bizId) {
        this.bizId = bizId;
    }
    
    public String getBizId() { return bizId; }
    public void setBizId(String bizId) { this.bizId = bizId; }
    
    public String getHolidays() { return holidays; }
    public void setHolidays(String holidays) { this.holidays = holidays; }
    
    public String getSummerTime() { return summerTime; }
    public void setSummerTime(String summerTime) { this.summerTime = summerTime; }
    
    public String getWinterTime() { return winterTime; }
    public void setWinterTime(String winterTime) { this.winterTime = winterTime; }
}
