import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * Simple demonstration of GlobalCalendar refactoring
 * Shows the transformation from multi-record to single-record design
 */
public class SimpleDemo {
    
    public static void main(String[] args) {
        System.out.println("=== GlobalCalendar Single Record Design Demo ===");
        
        // Create a GlobalCalendar object
        MockGlobalCalendar calendar = new MockGlobalCalendar();
        
        // Set holidays
        List<LocalDate> holidays = Arrays.asList(
            LocalDate.of(2024, 1, 1),   // New Year
            LocalDate.of(2024, 12, 25)  // Christmas
        );
        calendar.setHolidays(holidays);
        
        // Set summer time
        MockTimeRange summerTime = new MockTimeRange(
            LocalDate.of(2024, 6, 1), 
            LocalDate.of(2024, 8, 31)
        );
        calendar.setSummerTime(summerTime);
        
        // Demonstrate functionality
        LocalDate testDate = LocalDate.of(2024, 7, 15);
        System.out.println("Test date: " + testDate);
        System.out.println("Is holiday: " + calendar.isHoliday(testDate));
        System.out.println("Is summer time: " + calendar.isSummerTime(testDate));
        
        // Simulate database storage (JSON format)
        System.out.println("\n--- Database Storage Simulation ---");
        MockGlobalCalendarEntity entity = new MockGlobalCalendarEntity("default");
        entity.setHolidays("[\"2024-01-01\",\"2024-12-25\"]");
        entity.setSummerTime("{\"startDate\":\"2024-06-01\",\"endDate\":\"2024-08-31\"}");
        
        System.out.println("Stored holidays JSON: " + entity.getHolidays());
        System.out.println("Stored summer time JSON: " + entity.getSummerTime());
        
        System.out.println("\n=== Demo Complete ===");
        System.out.println("Key Benefits of Single Record Design:");
        System.out.println("1. Simplified data model - one record instead of many");
        System.out.println("2. JSON storage for complex data structures");
        System.out.println("3. Better performance - single query instead of multiple");
        System.out.println("4. Easier maintenance and backup");
        System.out.println("5. Atomic updates - all calendar data updated together");
    }
}

/**
 * Mock GlobalCalendar class
 */
class MockGlobalCalendar {
    private List<LocalDate> holidays;
    private MockTimeRange summerTime;
    private MockTimeRange winterTime;
    
    public List<LocalDate> getHolidays() { return holidays; }
    public void setHolidays(List<LocalDate> holidays) { this.holidays = holidays; }
    
    public MockTimeRange getSummerTime() { return summerTime; }
    public void setSummerTime(MockTimeRange summerTime) { this.summerTime = summerTime; }
    
    public MockTimeRange getWinterTime() { return winterTime; }
    public void setWinterTime(MockTimeRange winterTime) { this.winterTime = winterTime; }
    
    public boolean isHoliday(LocalDate date) {
        return holidays != null && holidays.contains(date);
    }
    
    public boolean isSummerTime(LocalDate date) {
        return summerTime != null && summerTime.contains(date);
    }
    
    public boolean isWinterTime(LocalDate date) {
        return winterTime != null && winterTime.contains(date);
    }
}

/**
 * Mock TimeRange class
 */
class MockTimeRange {
    private LocalDate startDate;
    private LocalDate endDate;
    
    public MockTimeRange(LocalDate startDate, LocalDate endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    public LocalDate getStartDate() { return startDate; }
    public LocalDate getEndDate() { return endDate; }
    
    public boolean contains(LocalDate date) {
        return date != null && startDate != null && endDate != null &&
               !date.isBefore(startDate) && !date.isAfter(endDate);
    }
}

/**
 * Mock GlobalCalendarEntity class (single record design)
 */
class MockGlobalCalendarEntity {
    private String bizId;
    private String holidays;      // JSON format
    private String summerTime;    // JSON format
    private String winterTime;    // JSON format
    
    public MockGlobalCalendarEntity(String bizId) {
        this.bizId = bizId;
    }
    
    public String getBizId() { return bizId; }
    public String getHolidays() { return holidays; }
    public void setHolidays(String holidays) { this.holidays = holidays; }
    public String getSummerTime() { return summerTime; }
    public void setSummerTime(String summerTime) { this.summerTime = summerTime; }
    public String getWinterTime() { return winterTime; }
    public void setWinterTime(String winterTime) { this.winterTime = winterTime; }
}
