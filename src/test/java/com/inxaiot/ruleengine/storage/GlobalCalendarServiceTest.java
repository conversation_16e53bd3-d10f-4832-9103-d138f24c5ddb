package com.inxaiot.ruleengine.storage;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeRange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GlobalCalendarService单元测试
 * 验证单记录设计的正确性
 */
@ExtendWith(MockitoExtension.class)
class GlobalCalendarServiceTest {

    @Mock
    private GlobalCalendarMapper globalCalendarMapper;

    @Mock
    private ObjectMapper objectMapper;

    private GlobalCalendarService globalCalendarService;

    @BeforeEach
    void setUp() {
        globalCalendarService = new GlobalCalendarService(globalCalendarMapper, objectMapper);
    }

    @Test
    void testLoadGlobalCalendar_WhenEntityExists() throws Exception {
        // 准备测试数据
        String bizId = "default";
        GlobalCalendarEntity entity = new GlobalCalendarEntity(bizId);
        entity.setHolidays("[\"2024-01-01\",\"2024-12-25\"]");
        entity.setSummerTime("{\"startDate\":\"2024-06-01\",\"endDate\":\"2024-08-31\"}");
        entity.setWinterTime("{\"startDate\":\"2024-12-01\",\"endDate\":\"2024-02-28\"}");

        // Mock mapper调用
        when(globalCalendarMapper.findGlobalCalendarByBizId(bizId)).thenReturn(entity);
        
        // Mock JSON反序列化
        when(objectMapper.readValue(eq("[\"2024-01-01\",\"2024-12-25\"]"), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 25)));
        
        TimeRange summerTime = new TimeRange(LocalDate.of(2024, 6, 1), LocalDate.of(2024, 8, 31), "夏令时间", true);
        when(objectMapper.readValue(eq("{\"startDate\":\"2024-06-01\",\"endDate\":\"2024-08-31\"}"), eq(TimeRange.class)))
            .thenReturn(summerTime);
        
        TimeRange winterTime = new TimeRange(LocalDate.of(2024, 12, 1), LocalDate.of(2024, 2, 28), "冬令时间", true);
        when(objectMapper.readValue(eq("{\"startDate\":\"2024-12-01\",\"endDate\":\"2024-02-28\"}"), eq(TimeRange.class)))
            .thenReturn(winterTime);

        // 执行测试
        GlobalCalendar result = globalCalendarService.loadGlobalCalendar();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getHolidays().size());
        assertTrue(result.getHolidays().contains(LocalDate.of(2024, 1, 1)));
        assertTrue(result.getHolidays().contains(LocalDate.of(2024, 12, 25)));
        assertNotNull(result.getSummerTime());
        assertNotNull(result.getWinterTime());

        // 验证mapper调用
        verify(globalCalendarMapper).findGlobalCalendarByBizId(bizId);
    }

    @Test
    void testLoadGlobalCalendar_WhenEntityNotExists() {
        // Mock mapper返回null
        when(globalCalendarMapper.findGlobalCalendarByBizId("default")).thenReturn(null);

        // 执行测试
        GlobalCalendar result = globalCalendarService.loadGlobalCalendar();

        // 验证结果 - 应该返回空的日历对象
        assertNotNull(result);
        assertTrue(result.getHolidays().isEmpty());
        assertNull(result.getSummerTime());
        assertNull(result.getWinterTime());
    }

    @Test
    void testSaveGlobalCalendar_NewRecord() throws Exception {
        // 准备测试数据
        GlobalCalendar globalCalendar = new GlobalCalendar();
        globalCalendar.setHolidays(Arrays.asList(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 25)));
        globalCalendar.setSummerTime(new TimeRange(LocalDate.of(2024, 6, 1), LocalDate.of(2024, 8, 31), "夏令时间", true));

        // Mock mapper调用
        when(globalCalendarMapper.existsGlobalCalendar("default")).thenReturn(false);
        when(globalCalendarMapper.insertGlobalCalendar(any(GlobalCalendarEntity.class))).thenReturn(1);
        
        // Mock JSON序列化
        when(objectMapper.writeValueAsString(anyList())).thenReturn("[\"2024-01-01\",\"2024-12-25\"]");
        when(objectMapper.writeValueAsString(any(TimeRange.class))).thenReturn("{\"startDate\":\"2024-06-01\",\"endDate\":\"2024-08-31\"}");

        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.saveGlobalCalendar(globalCalendar));

        // 验证mapper调用
        verify(globalCalendarMapper).existsGlobalCalendar("default");
        verify(globalCalendarMapper).insertGlobalCalendar(any(GlobalCalendarEntity.class));
        verify(globalCalendarMapper, never()).updateGlobalCalendar(any(GlobalCalendarEntity.class));
    }

    @Test
    void testSaveGlobalCalendar_UpdateExistingRecord() throws Exception {
        // 准备测试数据
        GlobalCalendar globalCalendar = new GlobalCalendar();
        globalCalendar.setHolidays(Arrays.asList(LocalDate.of(2024, 1, 1)));

        // Mock mapper调用
        when(globalCalendarMapper.existsGlobalCalendar("default")).thenReturn(true);
        when(globalCalendarMapper.updateGlobalCalendar(any(GlobalCalendarEntity.class))).thenReturn(1);
        
        // Mock JSON序列化
        when(objectMapper.writeValueAsString(anyList())).thenReturn("[\"2024-01-01\"]");

        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.saveGlobalCalendar(globalCalendar));

        // 验证mapper调用
        verify(globalCalendarMapper).existsGlobalCalendar("default");
        verify(globalCalendarMapper).updateGlobalCalendar(any(GlobalCalendarEntity.class));
        verify(globalCalendarMapper, never()).insertGlobalCalendar(any(GlobalCalendarEntity.class));
    }

    @Test
    void testRefreshCalendarCache() {
        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.refreshCalendarCache());
    }

    @Test
    void testGetSystemConfig() {
        // 执行测试
        java.util.Map<String, Object> config = globalCalendarService.getSystemConfig();

        // 验证结果
        assertNotNull(config);
        assertEquals("Asia/Shanghai", config.get("timezone"));
        assertEquals("zh_CN", config.get("locale"));
        assertEquals("yyyy-MM-dd", config.get("dateFormat"));
        assertEquals("HH:mm:ss", config.get("timeFormat"));
    }
}
