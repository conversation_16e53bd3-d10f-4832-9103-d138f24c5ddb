package com.inxaiot.ruleengine.core.engine;

import com.inxaiot.ruleengine.RuleEngineTestConfig;
import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.adapter.RuleAdapterService;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.common.context.SystemContextService;
import org.jeasy.rules.api.Facts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RuleEngineServiceImpl unit test
 * Test core functionality of rule engine service
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Import(RuleEngineTestConfig.class)
public class RuleEngineServiceImplTest {

    @Mock
    private RuleService ruleService;

    @Mock
    private RuleAdapterService ruleAdapterService;

    @Mock
    private SystemContextService systemContextService;

    @Mock
    private ThreadPoolTaskExecutor ruleEvaluationExecutor;

    @Mock
    private ActionExecutor actionExecutor;

    @Mock
    private DependencyAnalyzer dependencyAnalyzer;

    @Mock
    private FactsBuilder factsBuilder;

    private RuleEngineServiceImpl ruleEngineService;

    @BeforeEach
    void setUp() {
        ruleEngineService = new RuleEngineServiceImpl(
            ruleService,
            ruleAdapterService,
            systemContextService,
            ruleEvaluationExecutor,
            actionExecutor,
            dependencyAnalyzer,
            factsBuilder
        );
    }

    /**
     * Test processing device event - normal flow
     */
    @Test
    void testProcessDeviceEvent_Success() {
        // Given
        String deviceId = "temp_sensor_001";
        String pointId = "temperature";
        Object value = 30.0;

        // Mock async executor to execute task directly (sync test)
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock finding related rules
        RuleDefinition rule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceId))
            .thenReturn(Arrays.asList(rule));

        // Mock Facts building
        Facts facts = new Facts();
        facts.put("deviceId", deviceId);
        facts.put("pointId", pointId);
        facts.put("value", value);
        when(factsBuilder.buildCompleteFactsForRule(eq(rule), eq(deviceId), eq(pointId), eq(value)))
            .thenReturn(facts);

        // When
        ruleEngineService.processDeviceEvent(deviceId, pointId, value);

        // Then
        verify(ruleEvaluationExecutor).execute(any(Runnable.class));
        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceId);
        verify(factsBuilder).buildCompleteFactsForRule(eq(rule), eq(deviceId), eq(pointId), eq(value));
    }

    /**
     * Test processing device event - no relevant rules
     */
    @Test
    void testProcessDeviceEvent_NoRelevantRules() {
        // Given
        String deviceId = "unknown_device";
        String pointId = "unknown_point";
        Object value = 25.0;

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock no related rules found
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceId))
            .thenReturn(Arrays.asList());

        // When
        ruleEngineService.processDeviceEvent(deviceId, pointId, value);

        // Then
        verify(ruleEvaluationExecutor).execute(any(Runnable.class));
        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceId);
        // Should not call Facts builder
        verify(factsBuilder, never()).buildCompleteFactsForRule(any(), any(), any(), any());
    }

    /**
     * Test handling state change event - VALUE_CHANGED type
     */
    @Test
    void testHandleStateChangeEvent_ValueChanged() {
        // Given
        StateChangeEvent event = TestDataFactory.createStateChangeEvent(
            "temp_sensor_001", "temperature", 25.0, 30.0);

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock finding related rules
        RuleDefinition rule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRulesRelevantToDevice(event.getDeviceId()))
            .thenReturn(Arrays.asList(rule));

        // Mock Facts building
        Facts facts = new Facts();
        when(factsBuilder.buildCompleteFactsForRule(any(), any(), any(), any()))
            .thenReturn(facts);

        // When
        ruleEngineService.handleStateChangeEvent(event);

        // Then
        verify(ruleService).findAllEnabledRulesRelevantToDevice(event.getDeviceId());
        verify(factsBuilder).buildCompleteFactsForRule(any(), eq(event.getDeviceId()), 
            eq(event.getPointId()), eq(event.getNewValue()));
    }

    /**
     * Test triggering rule activation
     */
    @Test
    void testTriggerRuleActivation() {
        // Given
        long ruleId = 1L;
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId(ruleId);

        when(ruleService.findRuleById(ruleId)).thenReturn(rule);

        // When
        ruleEngineService.triggerRuleActivation(ruleId);

        // Then
        verify(ruleService).findRuleById(ruleId);
        // Verify rule is triggered for execution
    }

    /**
     * Test triggering rule deactivation
     */
    @Test
    void testTriggerRuleDeactivation() {
        // Given
        long ruleId = 1L;
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId(ruleId);

        when(ruleService.findRuleById(ruleId)).thenReturn(rule);

        // When
        ruleEngineService.triggerRuleDeactivation(ruleId);

        // Then
        verify(ruleService).findRuleById(ruleId);
    }

    /**
     * Test exception handling - exception during rule processing
     */
    @Test
    void testProcessDeviceEvent_ExceptionHandling() {
        // Given
        String deviceId = "temp_sensor_001";
        String pointId = "temperature";
        Object value = 30.0;

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock exception when finding rules
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceId))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then - should not throw exception, should be handled internally
        assertDoesNotThrow(() -> {
            ruleEngineService.processDeviceEvent(deviceId, pointId, value);
        });

        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceId);
    }
}
