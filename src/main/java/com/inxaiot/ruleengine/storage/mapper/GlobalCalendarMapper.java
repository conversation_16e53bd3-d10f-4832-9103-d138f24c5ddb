package com.inxaiot.ruleengine.storage.mapper;

import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 全局日历Mapper接口
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface GlobalCalendarMapper {
    
    /**
     * 插入日历记录
     */
    int insertCalendar(GlobalCalendarEntity calendar);
    
    /**
     * 根据ID更新日历记录
     */
    int updateCalendarById(GlobalCalendarEntity calendar);
    
    /**
     * 根据ID删除日历记录
     */
    int deleteCalendarById(@Param("id") Long id);

    /**
     * 根据ID查询日历记录
     */
    GlobalCalendarEntity findCalendarById(@Param("id") Long id);
    
    /**
     * 根据日期和类型查询日历记录
     */
    GlobalCalendarEntity findCalendarByDateAndType(@Param("calendarDate") LocalDate calendarDate, @Param("calendarType") String calendarType);
    
    /**
     * 查询所有日历记录
     */
    List<GlobalCalendarEntity> findAllCalendars();
    
    /**
     * 查询所有启用的日历记录
     */
    List<GlobalCalendarEntity> findAllEnabledCalendars();
    
    /**
     * 根据类型查询日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByType(@Param("calendarType") String calendarType);
    
    /**
     * 根据类型查询启用的日历记录
     */
    List<GlobalCalendarEntity> findEnabledCalendarsByType(@Param("calendarType") String calendarType);
    
    /**
     * 根据日期范围查询日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByDateRange(@Param("startDate") LocalDate startDate, 
                                                        @Param("endDate") LocalDate endDate);
    
    /**
     * 根据日期范围和类型查询日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByDateRangeAndType(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("calendarType") String calendarType);
    
    /**
     * 查询指定年份的日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByYear(@Param("year") Integer year);
    
    /**
     * 查询指定年份和类型的日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByYearAndType(@Param("year") Integer year, @Param("calendarType") String calendarType);
    
    /**
     * 查询指定月份的日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByYearMonth(@Param("year") Integer year, @Param("month") Integer month);
    
    /**
     * 批量插入日历记录
     */
    int batchInsertCalendars(@Param("calendars") List<GlobalCalendarEntity> calendars);
    
    /**
     * 批量删除日历记录
     */
    int batchDeleteCalendarsByDates(@Param("dates") List<LocalDate> dates);
    
    /**
     * 根据类型批量删除日历记录
     */
    int batchDeleteCalendarsByType(@Param("calendarType") String calendarType);
    
    /**
     * 批量更新日历启用状态
     */
    int batchUpdateCalendarEnabled(@Param("dates") List<LocalDate> dates, @Param("enabled") Boolean enabled);
    
    /**
     * 统计日历记录总数
     */
    int countAllCalendars();
    
    /**
     * 根据类型统计日历记录数
     */
    int countCalendarsByType(@Param("calendarType") String calendarType);
    
    /**
     * 检查指定日期是否存在日历记录
     */
    boolean existsCalendarByDate(@Param("calendarDate") LocalDate calendarDate);
    
    /**
     * 检查指定日期和类型是否存在日历记录
     */
    boolean existsCalendarByDateAndType(@Param("calendarDate") LocalDate calendarDate, @Param("calendarType") String calendarType);
    
    /**
     * 查询所有不同的日历类型
     */
    List<String> findAllCalendarTypes();
    
    /**
     * 分页查询日历记录
     */
    List<GlobalCalendarEntity> findCalendarsWithPagination(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 根据条件分页查询日历记录
     */
    List<GlobalCalendarEntity> findCalendarsByConditionWithPagination(@Param("calendarType") String calendarType,
                                                                      @Param("startDate") LocalDate startDate,
                                                                      @Param("endDate") LocalDate endDate,
                                                                      @Param("enabled") Boolean enabled,
                                                                      @Param("offset") Integer offset,
                                                                      @Param("limit") Integer limit);

    /**
     * 删除所有日历记录
     */
    int deleteAllCalendars();
}
