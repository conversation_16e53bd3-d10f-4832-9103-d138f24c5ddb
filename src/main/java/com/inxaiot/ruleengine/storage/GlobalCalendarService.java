package com.inxaiot.ruleengine.storage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 全局日历服务（单记录设计）
 * 提供全局日历的存储、查询、更新等功能
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-12-19
 */
@Service
@Transactional
public class GlobalCalendarService {

    private static final Logger logger = LoggerFactory.getLogger(GlobalCalendarService.class);

    private final GlobalCalendarMapper globalCalendarMapper;
    private final ObjectMapper objectMapper;

    // 缓存全局日历
    private GlobalCalendar cachedGlobalCalendar;
    private LocalDateTime lastCacheTime;
    private static final long CACHE_DURATION_HOURS = 24; // 缓存24小时
    private static final String DEFAULT_BIZ_ID = "default";

    @Autowired
    public GlobalCalendarService(GlobalCalendarMapper globalCalendarMapper, ObjectMapper objectMapper) {
        this.globalCalendarMapper = globalCalendarMapper;
        this.objectMapper = objectMapper;
    }

    /**
     * 加载全局日历
     */
    public GlobalCalendar loadGlobalCalendar() {
        return loadGlobalCalendar(DEFAULT_BIZ_ID);
    }

    /**
     * 根据业务ID加载全局日历
     */
    public GlobalCalendar loadGlobalCalendar(String bizId) {
        try {
            // 检查缓存
            if (cachedGlobalCalendar != null && lastCacheTime != null &&
                lastCacheTime.plusHours(CACHE_DURATION_HOURS).isAfter(LocalDateTime.now())) {
                logger.debug("Returning cached global calendar");
                return cachedGlobalCalendar;
            }

            GlobalCalendarEntity entity = globalCalendarMapper.findGlobalCalendarByBizId(bizId);
            GlobalCalendar globalCalendar;

            if (entity != null) {
                globalCalendar = convertEntityToGlobalCalendar(entity);
            } else {
                // 如果没有找到记录，创建默认的空日历
                globalCalendar = new GlobalCalendar();
                logger.info("No global calendar found for bizId: {}, returning empty calendar", bizId);
            }

            // 更新缓存
            cachedGlobalCalendar = globalCalendar;
            lastCacheTime = LocalDateTime.now();

            return globalCalendar;
        } catch (Exception e) {
            logger.error("Failed to load global calendar for bizId: {}", bizId, e);
            throw new RuntimeException("Failed to load global calendar", e);
        }
    }

    /**
     * 保存全局日历
     */
    public void saveGlobalCalendar(GlobalCalendar globalCalendar) {
        saveGlobalCalendar(globalCalendar, DEFAULT_BIZ_ID);
    }

    /**
     * 根据业务ID保存全局日历
     */
    public void saveGlobalCalendar(GlobalCalendar globalCalendar, String bizId) {
        try {
            GlobalCalendarEntity entity = convertGlobalCalendarToEntity(globalCalendar, bizId);

            if (globalCalendarMapper.existsGlobalCalendar(bizId)) {
                globalCalendarMapper.updateGlobalCalendar(entity);
                logger.info("Updated global calendar for bizId: {}", bizId);
            } else {
                globalCalendarMapper.insertGlobalCalendar(entity);
                logger.info("Inserted global calendar for bizId: {}", bizId);
            }

            // 清除缓存
            cachedGlobalCalendar = null;
            lastCacheTime = null;

        } catch (Exception e) {
            logger.error("Failed to save global calendar for bizId: {}", bizId, e);
            throw new RuntimeException("Failed to save global calendar", e);
        }
    }

    /**
     * 刷新日历缓存
     */
    public void refreshCalendarCache() {
        try {
            cachedGlobalCalendar = null;
            lastCacheTime = null;
            logger.info("Calendar cache refreshed");
        } catch (Exception e) {
            logger.error("Failed to refresh calendar cache", e);
            throw new RuntimeException("Failed to refresh calendar cache", e);
        }
    }

    /**
     * 获取系统配置
     */
    public Map<String, Object> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("timezone", "Asia/Shanghai");
            config.put("locale", "zh_CN");
            config.put("dateFormat", "yyyy-MM-dd");
            config.put("timeFormat", "HH:mm:ss");

            logger.debug("Retrieved system config");
            return config;
        } catch (Exception e) {
            logger.error("Failed to get system config", e);
            throw new RuntimeException("Failed to get system config", e);
        }
    }

    /**
     * 将数据库实体转换为全局日历对象
     */
    private GlobalCalendar convertEntityToGlobalCalendar(GlobalCalendarEntity entity) {
        try {
            GlobalCalendar globalCalendar = new GlobalCalendar();

            // 解析假期列表
            if (entity.getHolidays() != null && !entity.getHolidays().trim().isEmpty()) {
                List<LocalDate> holidays = objectMapper.readValue(entity.getHolidays(),
                    new TypeReference<List<LocalDate>>() {});
                globalCalendar.setHolidays(holidays);
            }

            // 解析夏令时间
            if (entity.getSummerTime() != null && !entity.getSummerTime().trim().isEmpty()) {
                TimeRange summerTime = objectMapper.readValue(entity.getSummerTime(), TimeRange.class);
                globalCalendar.setSummerTime(summerTime);
            }

            // 解析冬令时间
            if (entity.getWinterTime() != null && !entity.getWinterTime().trim().isEmpty()) {
                TimeRange winterTime = objectMapper.readValue(entity.getWinterTime(), TimeRange.class);
                globalCalendar.setWinterTime(winterTime);
            }

            logger.debug("Converted entity to global calendar for bizId: {}", entity.getBizId());
            return globalCalendar;
        } catch (JsonProcessingException e) {
            logger.error("Failed to convert entity to global calendar", e);
            throw new RuntimeException("Failed to convert entity to global calendar", e);
        }
    }

    /**
     * 将全局日历对象转换为数据库实体
     */
    private GlobalCalendarEntity convertGlobalCalendarToEntity(GlobalCalendar globalCalendar, String bizId) {
        try {
            GlobalCalendarEntity entity = new GlobalCalendarEntity(bizId);

            // 序列化假期列表
            if (globalCalendar.getHolidays() != null) {
                entity.setHolidays(objectMapper.writeValueAsString(globalCalendar.getHolidays()));
            }

            // 序列化夏令时间
            if (globalCalendar.getSummerTime() != null) {
                entity.setSummerTime(objectMapper.writeValueAsString(globalCalendar.getSummerTime()));
            }

            // 序列化冬令时间
            if (globalCalendar.getWinterTime() != null) {
                entity.setWinterTime(objectMapper.writeValueAsString(globalCalendar.getWinterTime()));
            }

            logger.debug("Converted global calendar to entity for bizId: {}", bizId);
            return entity;
        } catch (JsonProcessingException e) {
            logger.error("Failed to convert global calendar to entity", e);
            throw new RuntimeException("Failed to convert global calendar to entity", e);
        }
    }

}
