package com.inxaiot.ruleengine.storage.entity;

import java.time.LocalDateTime;

/**
 * 全局日历数据库实体类（单记录设计）
 * 对应global_calendar表
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-12-19
 */
public class GlobalCalendarEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务ID，预留扩展
     */
    private String bizId;

    /**
     * 假期日期列表（JSON格式）
     */
    private String holidays;

    /**
     * 夏令时起止时间（JSON格式）
     */
    private String summerTime;

    /**
     * 冬令时起止时间（JSON格式）
     */
    private String winterTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 构造函数
    public GlobalCalendarEntity() {
        this.bizId = "default";
    }

    public GlobalCalendarEntity(String bizId) {
        this.bizId = bizId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getHolidays() {
        return holidays;
    }

    public void setHolidays(String holidays) {
        this.holidays = holidays;
    }

    public String getSummerTime() {
        return summerTime;
    }

    public void setSummerTime(String summerTime) {
        this.summerTime = summerTime;
    }

    public String getWinterTime() {
        return winterTime;
    }

    public void setWinterTime(String winterTime) {
        this.winterTime = winterTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "GlobalCalendarEntity{" +
                "id=" + id +
                ", bizId='" + bizId + '\'' +
                ", holidays='" + holidays + '\'' +
                ", summerTime='" + summerTime + '\'' +
                ", winterTime='" + winterTime + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
