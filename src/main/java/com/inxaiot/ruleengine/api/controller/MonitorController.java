package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.storage.RuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控和日志API控制器
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/engine/monitor")
public class MonitorController {

    private static final Logger logger = LoggerFactory.getLogger(MonitorController.class);

    private final RuleService ruleService;
    private final StateManager stateManager;
    private final RuleEngineService ruleEngineService;

    @Autowired
    public MonitorController(RuleService ruleService, StateManager stateManager, RuleEngineService ruleEngineService) {
        this.ruleService = ruleService;
        this.stateManager = stateManager;
        this.ruleEngineService = ruleEngineService;
    }

    /**
     * 获取引擎运行状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getEngineStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 基本信息
            status.put("engineId", "rule-engine-1");
            status.put("version", "1.0.0");
            status.put("status", "RUNNING");
            status.put("startTime", System.currentTimeMillis() - 3600000); // 假设1小时前启动
            status.put("currentTime", System.currentTimeMillis());
            status.put("uptime", 3600000); // 运行时间（毫秒）

            // 规则统计
            Map<String, Object> ruleStats = new HashMap<>();
            ruleStats.put("totalRules", ruleService.countAllRules());
            ruleStats.put("enabledRules", ruleService.countEnabledRules());
            ruleStats.put("disabledRules", ruleService.countAllRules() - ruleService.countEnabledRules());
            status.put("ruleStatistics", ruleStats);

            // 设备状态统计
            Map<String, Object> deviceStats = stateManager.getStateStatistics();
            status.put("deviceStatistics", deviceStats);

            // 系统资源信息
            Map<String, Object> systemInfo = new HashMap<>();
            Runtime runtime = Runtime.getRuntime();
            systemInfo.put("totalMemory", runtime.totalMemory());
            systemInfo.put("freeMemory", runtime.freeMemory());
            systemInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            systemInfo.put("maxMemory", runtime.maxMemory());
            systemInfo.put("availableProcessors", runtime.availableProcessors());
            status.put("systemInfo", systemInfo);

            return ResponseEntity.ok(status);

        } catch (Exception e) {
            logger.error("Error getting engine status", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取健康检查信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();

            // 整体健康状态
            boolean isHealthy = true;
            health.put("status", "UP");

            // 检查各个组件
            Map<String, Object> components = new HashMap<>();

            // 数据库连接检查
            Map<String, String> value = new HashMap<>();
            try {
                ruleService.countAllRules();
                value.put("status", "UP");
                value.put("details", "Database connection is healthy");
                components.put("database", value);
            } catch (Exception e) {
                isHealthy = false;
                value.put("status", "DOWN");
                value.put("details", "Database connection failed: " + e.getMessage());
                components.put("database", value);
            }

            // 设备状态管理器检查
            Map<String, String> value1 = new HashMap<>();
            try {
                stateManager.getStateStatistics();
                value1.put("status", "UP");
                value1.put("details", "Device state manager is healthy");
                components.put("deviceStateManager", value1);
            } catch (Exception e) {
                isHealthy = false;
                value1.put("status", "DOWN");
                value1.put("details", "Device state manager failed: " + e.getMessage());
                components.put("deviceStateManager", value1);
            }

            // 规则引擎检查
            Map<String, String> value2 = new HashMap<>();
            value2.put("status", "UP");
            value2.put("details", "Rule engine is healthy");
            components.put("ruleEngine", value2);

            health.put("components", components);

            if (!isHealthy) {
                health.put("status", "DOWN");
                return ResponseEntity.status(503).body(health);
            }

            return ResponseEntity.ok(health);

        } catch (Exception e) {
            logger.error("Error performing health check", e);
            Map<String, Object> errorHealth = new HashMap<>();
            errorHealth.put("status", "DOWN");
            errorHealth.put("error", e.getMessage());
            return ResponseEntity.status(503).body(errorHealth);
        }
    }

    /**
     * 获取性能指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();

            // JVM指标
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvmMetrics = new HashMap<>();
            jvmMetrics.put("memory.total", runtime.totalMemory());
            jvmMetrics.put("memory.free", runtime.freeMemory());
            jvmMetrics.put("memory.used", runtime.totalMemory() - runtime.freeMemory());
            jvmMetrics.put("memory.max", runtime.maxMemory());
            jvmMetrics.put("processors", runtime.availableProcessors());
            metrics.put("jvm", jvmMetrics);

            // 规则引擎指标
            Map<String, Object> engineMetrics = new HashMap<>();
            engineMetrics.put("rules.total", ruleService.countAllRules());
            engineMetrics.put("rules.enabled", ruleService.countEnabledRules());
            engineMetrics.put("rules.executions.total", 0); // 需要实现执行计数
            engineMetrics.put("rules.executions.success", 0);
            engineMetrics.put("rules.executions.failed", 0);
            metrics.put("engine", engineMetrics);

            // 设备状态指标
            Map<String, Object> deviceMetrics = stateManager.getStateStatistics();
            metrics.put("devices", deviceMetrics);

            return ResponseEntity.ok(metrics);

        } catch (Exception e) {
            logger.error("Error getting metrics", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取规则执行日志
     */
    @GetMapping("/logs/rules")
    public ResponseEntity<List<Map<String, Object>>> getRuleExecutionLogs(
            @RequestParam(required = false) String ruleId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // 这里需要实现日志查询逻辑
            // List<Map<String, Object>> logs = logService.getRuleExecutionLogs(ruleId, status, page, size);

            // 模拟返回数据
            List<Map<String, Object>> logs = new ArrayList<>();
            Map<String, Object> e = new HashMap<>();
            e.put("id", 1);
            e.put("ruleId", "rule_001");
            e.put("executionId", "exec_001");
            e.put("status", "SUCCESS");
            e.put("executionTime", LocalDateTime.now().minusMinutes(5).toString());
            e.put("duration", 150);
            e.put("triggerDevice", "device_001");
            e.put("result", "Action executed successfully");
            logs.add(e);

            return ResponseEntity.ok(logs);

        } catch (Exception e) {
            logger.error("Error getting rule execution logs", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取系统日志
     */
    @GetMapping("/logs/system")
    public ResponseEntity<List<Map<String, Object>>> getSystemLogs(
            @RequestParam(required = false) String level,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // 这里需要实现系统日志查询逻辑
            // List<Map<String, Object>> logs = logService.getSystemLogs(level, page, size);

            // 模拟返回数据
            List<Map<String, Object>> logs = new ArrayList<>();
            Map<String, Object> e = new HashMap<>();
            e.put("timestamp", LocalDateTime.now().toString());
            e.put("level", "INFO");
            e.put("logger", "com.inxaiot.ruleengine.core.engine.RuleEngineTriggerServiceImpl");
            e.put("message", "Rule engine started successfully");
            e.put("thread", "main");
            logs.add(e);

            return ResponseEntity.ok(logs);

        } catch (Exception e) {
            logger.error("Error getting system logs", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/logs/cleanup")
    public ResponseEntity<String> cleanupLogs(@RequestParam(defaultValue = "30") int daysToKeep) {
        try {
            // 这里需要实现日志清理逻辑
            // int deletedCount = logService.cleanupLogs(daysToKeep);
            int deletedCount = 0; // 模拟

            return ResponseEntity.ok("Cleaned up " + deletedCount + " log entries older than " + daysToKeep + " days");

        } catch (Exception e) {
            logger.error("Error cleaning up logs", e);
            return ResponseEntity.status(500).body("Error cleaning up logs: " + e.getMessage());
        }
    }

    /**
     * 触发垃圾回收
     */
    @PostMapping("/gc")
    public ResponseEntity<Map<String, Object>> triggerGarbageCollection() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long beforeGC = runtime.totalMemory() - runtime.freeMemory();

            System.gc();

            long afterGC = runtime.totalMemory() - runtime.freeMemory();
            long freedMemory = beforeGC - afterGC;

            Map<String, Object> result = new HashMap<>();
            result.put("memoryBeforeGC", beforeGC);
            result.put("memoryAfterGC", afterGC);
            result.put("freedMemory", freedMemory);
            result.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error triggering garbage collection", e);
            return ResponseEntity.status(500).build();
        }
    }
}