package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.storage.RuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则管理API控制器
 * 提供规则的CRUD操作和管理功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/engine/rules")
@CrossOrigin(origins = "*")
public class RuleController {

    private static final Logger logger = LoggerFactory.getLogger(RuleController.class);

    private final RuleService ruleService;
    private final RuleEngineService ruleEngineService;

    @Autowired
    public RuleController(RuleService ruleService, RuleEngineService ruleEngineService) {
        this.ruleService = ruleService;
        this.ruleEngineService = ruleEngineService;
    }

    /**
     * 添加或更新规则
     */
    @PostMapping
    public ResponseEntity<ApiResponse<String>> addOrUpdateRule(@Valid @RequestBody RuleDefinition ruleDefinition) {
        try {
            if (ruleDefinition.getRuleId() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("Rule ID cannot be empty"));
            }

            ruleService.saveRule(ruleDefinition);

            logger.info("Rule saved successfully: {}", ruleDefinition.getRuleId());
            return ResponseEntity.ok(ApiResponse.success("Rule saved successfully: " + ruleDefinition.getRuleId()));

        } catch (Exception e) {
            logger.error("Error saving rule: {}", ruleDefinition.getRuleId(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error saving rule: " + e.getMessage()));
        }
    }

    /**
     * 根据规则ID获取规则
     */
    @GetMapping("/{ruleId}")
    public ResponseEntity<ApiResponse<RuleDefinition>> getRule(@PathVariable long ruleId) {
        try {
            RuleDefinition rule = ruleService.findRuleById(ruleId);
            if (rule != null) {
                return ResponseEntity.ok(ApiResponse.success(rule));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error getting rule: {}", ruleId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error getting rule: " + e.getMessage()));
        }
    }

    /**
     * 获取所有规则
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<RuleDefinition>>> getAllRules(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String bizId,
            @RequestParam(required = false) String groupId) {
        try {
            List<RuleDefinition> rules;

            if (enabled != null && enabled) {
                rules = ruleService.findAllEnabledRules();
            } else if (deviceId != null && !deviceId.trim().isEmpty()) {
                rules = ruleService.findEnabledRulesByTargetDeviceId(deviceId);
            } else if (bizId != null && !bizId.trim().isEmpty()) {
                rules = new ArrayList<>();
                rules.add(ruleService.findRuleByBizId(bizId));
            } else if (groupId != null && !groupId.trim().isEmpty()) {
                rules = ruleService.findRulesByGroupId(groupId);
            } else {
                rules = ruleService.findAllRules();
            }

            return ResponseEntity.ok(ApiResponse.success(rules));

        } catch (Exception e) {
            logger.error("Error getting rules", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error getting rules: " + e.getMessage()));
        }
    }

    /**
     * 删除规则
     */
    @DeleteMapping("/{ruleId}")
    public ResponseEntity<ApiResponse<String>> deleteRule(@PathVariable Long ruleId) {
        try {
            if (!ruleService.existsRule(ruleId)) {
                return ResponseEntity.notFound().build();
            }

            ruleService.deleteRuleById(ruleId);
            logger.info("Rule deleted successfully: {}", ruleId);
            return ResponseEntity.ok(ApiResponse.success("Rule deleted successfully: " + ruleId));

        } catch (Exception e) {
            logger.error("Error deleting rule: {}", ruleId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error deleting rule: " + e.getMessage()));
        }
    }

    /**
     * 批量操作规则
     */
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<String>> batchOperateRules(@RequestBody BatchRuleRequest request) {
        try {
            switch (request.getOperation().toUpperCase()) {
                case "CREATE":
                    ruleService.batchSaveRules(request.getRules());
                    break;
                case "DELETE":
                    ruleService.batchDeleteRules(request.getRuleIds());
                    break;
                case "ENABLE":
                    ruleService.batchUpdateRuleEnabledByIds(request.getRuleIds(), true);
                    break;
                case "DISABLE":
                    ruleService.batchUpdateRuleEnabledByIds(request.getRuleIds(), false);
                    break;
                default:
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error("Unsupported operation: " + request.getOperation()));
            }

            logger.info("Batch operation {} completed successfully", request.getOperation());
            return ResponseEntity.ok(ApiResponse.success("Batch operation completed successfully"));

        } catch (Exception e) {
            logger.error("Error in batch operation: {}", request.getOperation(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error in batch operation: " + e.getMessage()));
        }
    }

    /**
     * 获取规则统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRuleStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRules", ruleService.countAllRules());
            stats.put("enabledRules", ruleService.countEnabledRules());
            stats.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(ApiResponse.success(stats));

        } catch (Exception e) {
            logger.error("Error getting rule statistics", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error getting rule statistics: " + e.getMessage()));
        }
    }

    /**
     * 刷新规则缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<String>> refreshRules() {
        try {
            ruleService.reloadRules();
            logger.info("Rule cache refreshed manually");
            return ResponseEntity.ok(ApiResponse.success("Rule cache refreshed successfully"));

        } catch (Exception e) {
            logger.error("Error refreshing rule cache", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error refreshing rule cache: " + e.getMessage()));
        }
    }

    /**
     * 测试规则触发
     */
    @PostMapping("/test/{ruleId}")
    public ResponseEntity<ApiResponse<String>> testRule(@PathVariable String ruleId,
                                                        @RequestBody Map<String, Object> testData) {
        try {
            // 这里可以实现规则测试逻辑
            // 例如：模拟设备事件，检查规则是否会被触发

            String deviceId = (String) testData.get("deviceId");
            String pointId = (String) testData.get("pointId");
            Object value = testData.get("value");

            if (deviceId != null && pointId != null && value != null) {
                ruleEngineService.processDeviceEvent(deviceId, pointId, value);
                return ResponseEntity.ok(ApiResponse.success("Test event sent for rule: " + ruleId));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Test data must include deviceId, pointId, and value"));
            }

        } catch (Exception e) {
            logger.error("Error testing rule: {}", ruleId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error testing rule: " + e.getMessage()));
        }
    }

    /**
     * 批量规则操作请求
     */
    public static class BatchRuleRequest {
        private String operation; // CREATE, DELETE, ENABLE, DISABLE
        private List<Long> ruleIds;
        private List<RuleDefinition> rules;

        // Getters and Setters
        public String getOperation() { return operation; }
        public void setOperation(String operation) { this.operation = operation; }

        public List<Long> getRuleIds() { return ruleIds; }
        public void setRuleIds(List<Long> ruleIds) { this.ruleIds = ruleIds; }

        public List<RuleDefinition> getRules() { return rules; }
        public void setRules(List<RuleDefinition> rules) { this.rules = rules; }
    }

    /**
     * API响应包装类
     */
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        private long timestamp;

        public ApiResponse(boolean success, String message, T data) {
            this.success = success;
            this.message = message;
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }

        public static <T> ApiResponse<T> success(T data) {
            return new ApiResponse<>(true, "Success", data);
        }

        public static <T> ApiResponse<T> success(String message) {
            return new ApiResponse<>(true, message, null);
        }

        public static <T> ApiResponse<T> error(String message) {
            return new ApiResponse<>(false, message, null);
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public T getData() { return data; }
        public void setData(T data) { this.data = data; }

        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}