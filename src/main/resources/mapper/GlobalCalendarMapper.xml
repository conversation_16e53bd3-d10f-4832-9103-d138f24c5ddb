<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper">

    <!-- 结果映射 -->
    <resultMap id="GlobalCalendarResultMap" type="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="calendar_date" property="calendarDate" jdbcType="DATE"/>
        <result column="calendar_type" property="calendarType" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, calendar_date, calendar_type, description, enabled, create_time, update_time
    </sql>

    <!-- 插入日历记录 -->
    <insert id="insertCalendar" parameterType="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO global_calendar (
            calendar_date, calendar_type, description, enabled
        ) VALUES (
            #{calendarDate}, #{calendarType}, #{description}, #{enabled}
        )
    </insert>

    <!-- 根据ID更新日历记录 -->
    <update id="updateCalendarById" parameterType="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity">
        UPDATE global_calendar SET
            calendar_date = #{calendarDate},
            calendar_type = #{calendarType},
            description = #{description},
            enabled = #{enabled}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除日历记录 -->
    <delete id="deleteCalendarById" parameterType="java.lang.Long">
        DELETE FROM global_calendar WHERE id = #{id}
    </delete>
    

    <!-- 根据ID查询日历记录 -->
    <select id="findCalendarById" parameterType="java.lang.Long" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE id = #{id}
    </select>

    <!-- 根据日期查询日历记录 -->
    <select id="findCalendarsByDate" parameterType="java.time.LocalDate" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_date = #{calendarDate}
        ORDER BY calendar_type
    </select>

    <!-- 根据日期和类型查询日历记录 -->
    <select id="findCalendarByDateAndType" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_date = #{calendarDate} AND calendar_type = #{calendarType}
    </select>

    <!-- 查询所有日历记录 -->
    <select id="findAllCalendars" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        ORDER BY calendar_date DESC, calendar_type
    </select>

    <!-- 查询所有启用的日历记录 -->
    <select id="findAllEnabledCalendars" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE enabled = 1
        ORDER BY calendar_date DESC, calendar_type
    </select>

    <!-- 根据类型查询日历记录 -->
    <select id="findCalendarsByType" parameterType="java.lang.String" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_type = #{calendarType}
        ORDER BY calendar_date DESC
    </select>

    <!-- 根据类型查询启用的日历记录 -->
    <select id="findEnabledCalendarsByType" parameterType="java.lang.String" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_type = #{calendarType} AND enabled = 1
        ORDER BY calendar_date DESC
    </select>

    <!-- 根据日期范围查询日历记录 -->
    <select id="findCalendarsByDateRange" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY calendar_date DESC, calendar_type
    </select>

    <!-- 根据日期范围和类型查询日历记录 -->
    <select id="findCalendarsByDateRangeAndType" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE calendar_date BETWEEN #{startDate} AND #{endDate} 
        AND calendar_type = #{calendarType}
        ORDER BY calendar_date DESC
    </select>

    <!-- 查询指定年份的日历记录 -->
    <select id="findCalendarsByYear" parameterType="java.lang.Integer" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE strftime('%Y', calendar_date) = #{year}
        ORDER BY calendar_date DESC, calendar_type
    </select>

    <!-- 查询指定年份和类型的日历记录 -->
    <select id="findCalendarsByYearAndType" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE strftime('%Y', calendar_date) = #{year} AND calendar_type = #{calendarType}
        ORDER BY calendar_date DESC
    </select>

    <!-- 查询指定月份的日历记录 -->
    <select id="findCalendarsByYearMonth" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE strftime('%Y', calendar_date) = #{year} 
        AND strftime('%m', calendar_date) = printf('%02d', #{month})
        ORDER BY calendar_date DESC, calendar_type
    </select>

    <!-- 批量插入日历记录 -->
    <insert id="batchInsertCalendars" parameterType="java.util.List">
        INSERT INTO global_calendar (calendar_date, calendar_type, description, enabled) VALUES
        <foreach collection="calendars" item="calendar" separator=",">
            (#{calendar.calendarDate}, #{calendar.calendarType}, #{calendar.description}, #{calendar.enabled})
        </foreach>
    </insert>

    <!-- 批量删除日历记录 -->
    <delete id="batchDeleteCalendarsByDates">
        DELETE FROM global_calendar
        WHERE calendar_date IN
        <foreach collection="dates" item="date" open="(" separator="," close=")">
            #{date}
        </foreach>
    </delete>

    <!-- 根据类型批量删除日历记录 -->
    <delete id="batchDeleteCalendarsByType" parameterType="java.lang.String">
        DELETE FROM global_calendar WHERE calendar_type = #{calendarType}
    </delete>

    <!-- 批量更新日历启用状态 -->
    <update id="batchUpdateCalendarEnabled">
        UPDATE global_calendar SET enabled = #{enabled}
        WHERE calendar_date IN
        <foreach collection="dates" item="date" open="(" separator="," close=")">
            #{date}
        </foreach>
    </update>

    <!-- 统计日历记录总数 -->
    <select id="countAllCalendars" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM global_calendar
    </select>

    <!-- 根据类型统计日历记录数 -->
    <select id="countCalendarsByType" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM global_calendar WHERE calendar_type = #{calendarType}
    </select>

    <!-- 检查指定日期是否存在日历记录 -->
    <select id="existsCalendarByDate" parameterType="java.time.LocalDate" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM global_calendar WHERE calendar_date = #{calendarDate}
    </select>

    <!-- 检查指定日期和类型是否存在日历记录 -->
    <select id="existsCalendarByDateAndType" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM global_calendar 
        WHERE calendar_date = #{calendarDate} AND calendar_type = #{calendarType}
    </select>

    <!-- 查询所有不同的日历类型 -->
    <select id="findAllCalendarTypes" resultType="java.lang.String">
        SELECT DISTINCT calendar_type FROM global_calendar ORDER BY calendar_type
    </select>

    <!-- 分页查询日历记录 -->
    <select id="findCalendarsWithPagination" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        ORDER BY calendar_date DESC, calendar_type
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 根据条件分页查询日历记录 -->
    <select id="findCalendarsByConditionWithPagination" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        <where>
            <if test="calendarType != null and calendarType != ''">
                AND calendar_type = #{calendarType}
            </if>
            <if test="startDate != null">
                AND calendar_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND calendar_date &lt;= #{endDate}
            </if>
            <if test="enabled != null">
                AND enabled = #{enabled}
            </if>
        </where>
        ORDER BY calendar_date DESC, calendar_type
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 删除所有日历记录 -->
    <delete id="deleteAllCalendars">
        DELETE FROM global_calendar
    </delete>

</mapper>
